'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';

import { Button } from '@/components/atoms/Button/Button';
import { Input } from '@/components/atoms/Input/Input';
import { FormField } from '@/components/molecules/FormField/FormField';
import { CheckboxItem } from '@/components/molecules/CheckboxItem/CheckboxItem';
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Mail, Lock, ChevronUp, ChevronDown, Building, Presentation } from 'lucide-react';

import { LoginValues, loginSchema } from './SignInForm.schema';
import { cn } from '@/utils/cn';

export default function SignInForm() {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showSchoolCode, setShowSchoolCode] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
      schoolCode: '',
    },
  });

  const onSubmit = async (data: LoginValues) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await signIn('credentials', {
        email: data.email,
        password: data.password,
        schoolCode: data.schoolCode,
        redirect: false,
      });

      if (response?.error) {
        setError(response.error);
        return;
      }

      router.push('/'); // Redirect to dashboard on success
      router.refresh();
    } catch (err: any) {
      setError(err.message || 'An error occurred during sign in');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {error && (
          <div className="mb-4">
            <AlertMessage
              type="error"
              message={error}
            />
          </div>
        )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
        <FormField
          label="Email"
          error={errors.email?.message}
          icon={<Mail size={16} className="text-gray-500" />}
        >
          <Input
            type="email"
            placeholder="Enter your email"
            className={cn(
              errors.email && 'border-red-500 focus:border-red-500'
            )}
            {...register('email')}
          />
        </FormField>

        <FormField
          label="Password"
          error={errors.password?.message}
          icon={<Lock size={16} className="text-gray-500" />}
        >
          <PasswordInput
            placeholder="Enter your password"
            className={cn(
              errors.password && 'border-red-500 focus:border-red-500'
            )}
            {...register('password')}
          />
        </FormField>

        <div className="flex items-center justify-between">
          <CheckboxItem
            label="Remember Me"
            {...register('rememberMe')}
          />
          <a
            href="/auth/forgot-password"
            className="text-sm font-medium text-primary hover:text-primary-focus transition-colors"
          >
            Forgot Password?
          </a>
        </div>

        <div className="pt-2">
          <Button
            type="button"
            variant="ghost"
            className="w-full justify-between mb-4"
            onClick={() => setShowSchoolCode(!showSchoolCode)}
          >
            <span className="text-sm font-medium">
              {showSchoolCode ? 'Hide Institution Code' : 'Have an Institution Code?'}
            </span>
            {showSchoolCode ? (
              <ChevronUp size={16} className="text-gray-500" />
            ) : (
              <ChevronDown size={16} className="text-gray-500" />
            )}
          </Button>

          {showSchoolCode && (
            <div className="mb-4">
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <FormField
                  label="Institution Code"
                  error={errors.schoolCode?.message}
                  icon={<Building size={16} className="text-gray-500" />}
                >
                  <Input
                    type="text"
                    placeholder="Enter your institution code"
                    className={cn(
                      errors.schoolCode && 'border-red-500 focus:border-red-500'
                    )}
                    {...register('schoolCode')}
                  />
                </FormField>
              </motion.div>
            </div>
          )}

          <Button
            type="submit"
            variant="primary"
            className="w-full"
            isLoading={isLoading}
          >
            Sign In
          </Button>
        </div>
      </form>

      <div className="mt-8 pt-6 border-t border-gray-200">
        <div className="text-center">
          <h3 className="text-sm font-medium">New to EduSG?</h3>
          <div className="mt-3">
            <Button
              href="/auth/sign-up"
              variant="outline"
              className="w-full"
            >
              Create an Account
            </Button>
          </div>
          <div className="mt-3">
            <Button
              href="/demo"
              variant="ghost"
              className="w-full text-sm"
            >
              <Presentation size={16} className="mr-2 text-gray-500" />
              Try Demo Access
            </Button>
          </div>
        </div>
      </div>
      </motion.div>
    </div>
  );
}
