'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { CustomImage } from '@/components/atoms/CustomImage/CustomImage';
import Icon from '@/components/atoms/Icon/Icon';
import { cn } from '@/utils/cn';
import { Users, Shield, Edit, Calculator } from 'lucide-react';

interface AuthTemplateProps {
  children: ReactNode;
  className?: string;
}

export default function AuthTemplate({ children, className }: AuthTemplateProps) {
  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Left Section - Illustration and Branding */}
      <div className="bg-blue-100 md:w-1/2 p-8 flex flex-col justify-between relative overflow-hidden">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
        {/* Logo */}
        <div className="flex items-center space-x-2 z-10">
          <Icon variant="graduation-cap" size={6} className="text-primary" />
          <h1 className="text-2xl font-bold text-primary">EduSG</h1>
        </div>

          {/* Main Illustration */}
          <div className="flex justify-center items-center flex-grow z-10">
            <div className="max-w-md w-full h-80">
              <CustomImage
                src="/assets/sign-in.png"
                alt="Education Illustration"
                className="w-full h-full"
              />
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-8 space-y-4 z-10">
            <div className="flex items-center space-x-2">
              <Users size={16} className="text-primary" />
              <p className="text-sm font-medium">Trusted by 50,000+ students across Singapore</p>
            </div>
            <div className="flex items-center space-x-2">
              <Shield size={16} className="text-primary" />
              <p className="text-sm font-medium">MOE-compliant educational platform</p>
            </div>
          </div>

          {/* Background Decorative Elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-blue-200 rounded-full -mr-32 -mt-16 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-blue-200 rounded-full -ml-32 -mb-16 opacity-50"></div>
        </motion.div>
      </div>

      {/* Right Section - Content */}
      <div className={cn(
        'md:w-1/2 p-8 flex flex-col justify-center items-center',
        className
      )}>
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-2">Welcome to EduSG</h2>
            <p className="text-gray-600">
              Please sign in to continue to your educational journey
            </p>
          </div>

          {children}

          {/* Singapore Education Theme */}
          <div className="mt-12 pt-6 border-t border-gray-200">
            <div className="flex justify-center space-x-6">
              <Icon variant="book-open" size={4} className="text-gray-400 hover:text-primary transition-colors" />
              <Edit size={16} className="text-gray-400 hover:text-primary transition-colors" />
              <Calculator size={16} className="text-gray-400 hover:text-primary transition-colors" />
              <Icon variant="globe" size={4} className="text-gray-400 hover:text-primary transition-colors" />
            </div>
          </div>
        </div>
        </motion.div>
      </div>
    </div>
  );
}
