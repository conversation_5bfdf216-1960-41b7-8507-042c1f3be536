# Icon Overlap Fixes

This document outlines the fixes implemented to resolve icon overlap issues in input components.

## Problem

Icons were overlapping with input text content in various components, making text difficult to read and creating poor user experience.

## Solution

### 1. Enhanced Input Component

Updated `components/atoms/Input/Input.tsx` to support dynamic padding based on icon presence:

- Added `hasLeftIcon` and `hasRightIcon` props
- Implemented conditional padding classes:
  - `pl-10` when left icon is present
  - `pr-10` when right icon is present
  - Default `px-4` when no icons

### 2. Updated FormField Component

Enhanced `components/molecules/FormField/FormField.tsx`:

- Automatically detects Input components and passes icon props
- Improved icon positioning with proper z-index
- Better styling for icon containers

### 3. Fixed PasswordInput Component

Updated `components/molecules/PasswordInput/PasswordInput.tsx`:

- Uses new Input props for proper padding
- Added z-index to prevent overlap
- Removed manual padding override

### 4. Created InputWithIcon Component

New reusable component `components/molecules/InputWithIcon/InputWithIcon.tsx`:

- Consistent icon positioning across the codebase
- Support for both left and right icons
- Clickable right icons with proper accessibility
- Automatic padding management

### 5. Updated Search Components

Fixed the following components:
- `UserTableSearchBar`
- `SchoolTableHeader`
- `RHFCombobox`
- `examination-formats` page

## Usage Examples

### Basic Input with Icons

```tsx
import { InputWithIcon } from '@/components/molecules/InputWithIcon';
import { Search, X } from 'lucide-react';

// Search input with clear button
<InputWithIcon
  placeholder="Search..."
  leftIcon={<Search size={18} />}
  rightIcon={searchTerm ? <X size={16} /> : undefined}
  onRightIconClick={searchTerm ? clearSearch : undefined}
  rightIconAriaLabel="Clear search"
/>
```

### FormField with Icon

```tsx
import { FormField } from '@/components/molecules/FormField/FormField';
import { Input } from '@/components/atoms/Input/Input';
import { Mail } from 'lucide-react';

<FormField
  label="Email"
  icon={<Mail size={16} />}
>
  <Input type="email" placeholder="Enter your email" />
</FormField>
```

### Password Input

```tsx
import { PasswordInput } from '@/components/molecules/PasswordInput/PasswordInput';

<PasswordInput
  placeholder="Enter password"
  showStrengthMeter={true}
/>
```

## Key Improvements

1. **Consistent Spacing**: All inputs now have proper padding to prevent icon overlap
2. **Better Accessibility**: Proper ARIA labels and focus management
3. **Reusable Components**: InputWithIcon can be used across the codebase
4. **Z-index Management**: Icons are properly layered to prevent overlap
5. **Responsive Design**: Icons work well on all screen sizes

## Testing

To test the fixes:

1. Check that text doesn't overlap with icons in search inputs
2. Verify password visibility toggle works without text overlap
3. Test form fields with left icons have proper spacing
4. Ensure all interactive icons are clickable and accessible
